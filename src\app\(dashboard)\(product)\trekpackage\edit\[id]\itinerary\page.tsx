'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { AddItineraryForm } from '@/modules/product/component/add-itinerary-form'
import { ItineraryList } from '@/modules/product/component/itinerary-list'
import { EditTabs } from '@/modules/product/component/edit-tabs'
import { useGetPackageItineraryById } from '@/modules/package-itinerary/queries/get-package-itinerary-by-id'
import { useCreatePackageItinerary } from '@/modules/package-itinerary/mutations/create-package-itinerary'
import { useUpdatePackageItinerary } from '@/modules/package-itinerary/mutations/update-package-itinerary'
import { useDeletePackageItinerary } from '@/modules/package-itinerary/mutations/delete-package-itinerary'
import { IPackageItinerary } from '@/types/package_'
import { toast } from 'sonner'

export default function EditItineraryPage() {
  const { packageId } = useParams() as { packageId: string }

  const { data, isLoading, isError } = useGetPackageItineraryById(packageId)

  const createMutation = useCreatePackageItinerary()
  const updateMutation = useUpdatePackageItinerary(packageId)
  const deleteMutation = useDeletePackageItinerary(packageId)

  const [items, setItems] = useState<IPackageItinerary[]>([])
  const [editingItem, setEditingItem] = useState<IPackageItinerary | null>(null)

  useEffect(() => {
    if (data?.data) {
      // Handle both single item and array responses
      const itineraries = Array.isArray(data.data) ? data.data : [data.data]
      setItems(itineraries)
    }
  }, [data])

  const onAddItinerary = (newItem: Omit<IPackageItinerary, 'id' | 'createdAt' | 'updatedAt'> & { imageFile?: File }) => {
    createMutation.mutate({ ...newItem, packageId }, {
      onSuccess: (res) => {
        setItems([...items, res.data])
      },
    })
  }

  const onUpdateItinerary = (updatedItem) => {
    updateMutation.mutate(
      { ...updatedItem, packageId },
      {
        onSuccess: (res) => {
          setItems(items.map(i => (i.id === res.data.id ? res.data : i)))
          setEditingItem(null)
          toast.success('Itinerary updated successfully')
        },
        onError: () => {
          toast.error('Error updating itinerary')
        },
      }
    )
  }

  const onDeleteItinerary = (id) => {
    deleteMutation.mutate(id, {
      onSuccess: () => {
        setItems(items.filter(i => i.id !== id))
        toast.success('Itinerary deleted successfully')
      },
      onError: () => {
        toast.error('Error deleting itinerary')
      },
    })
  }

  if (isLoading) return <div>Loading itinerary...</div>
  if (isError) return <div>Error loading itinerary data.</div>

  return (
    <div className="min-h-screen p-6 bg-gray-50">
      <EditTabs packageId={packageId} />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <AddItineraryForm
          onAddItinerary={onAddItinerary}
          onUpdateItinerary={onUpdateItinerary}
          editingItem={editingItem}
          onCancelEdit={() => setEditingItem(null)}
        />

        <ItineraryList items={items} onEdit={(id) => setEditingItem(items.find(i => i.id === id))} onDelete={onDeleteItinerary} />
      </div>
    </div>
  )
}
